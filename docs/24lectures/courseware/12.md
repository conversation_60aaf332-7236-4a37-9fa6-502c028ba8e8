---
title: 数据分析与Python实现（2）
---

![](https://images.jieyu.ai/images/2023/07/lesson12-outline.png)

在学习了数据分布知识之后，我们来思考这样一个问题，上证指数究竟是一种什么样的分布？

如果它属于任何一种已知的分布，这将是一个统计推断（或者假设检验）问题。如果它是一种神秘、未知的分布，那么我们就退而求其次，尝试回答上次课留下的问题：

!!! Question
    能否基于过去1000个交易日的表现，推断当上证下跌4%之后，继续下跌的概率是？


## 1. 统计推断方法

### 1.1. 分位图

分位图（quantile-quantile plot）又称Q-Q图，在统计学中是通过比较两个概率分布的分位数，来比较这两个概率分布的一种方法。

Q-Q图是基于这样一个原理：

假设X是一个实数集，以$[X_i, X_i], X_i \in X$为坐标的所有点，都会落在 $y = x$，即一条过零点，45度角向上的直线上。

如果X是随机变量，Y是它的理论分布，那么对X, Y的n次抽样结果进行排序后得到的$\bar{X}, \bar{Y}$，以$[\bar{X_i}, \bar{Y_i}], X_i \in \bar{X}, Y_i \in \bar{Y}$为坐标的点，也应该落在$y = x$这条直线附近（因为有随机性，所以不可能完全相等）。

这里我们先以标准正态分布为例进行说明。

```python
import matplotlib.pyplot as plt
from scipy import stats
import numpy as np

fig, (ax1,ax2,ax3) = plt.subplots(nrows=1,ncols=3, figsize = (9, 3))

n = 1000
X = sorted(stats.norm.rvs(size=n))
Y = sorted(stats.norm.rvs(size=n))

ax1.scatter(np.arange(n), X, s=1)
ax1.text(100, 3, "X")
ax3.scatter(np.arange(n), Y, s=1)
ax3.text(100, 2.8, "Y")

ax2.scatter(X, Y, s=1)
ax2.plot(X,X, color='orange')
ax2.text(-2, 3, "y = x")
```

我们对标准正态分布分别作两次size = 1000的取样。第一次记为 X，假定它来自随机样本。第二次记为Y，是对理论分布的一次采样。然后我们对这两个数组进行排序，分别作图，我们会得到下图：

![](https://images.jieyu.ai/images/2023/07/lesson12-qq-plot-0.png)

从视觉上看，左图与右图几乎一致，除了开始和结束处。我们知道，在这些地方分布已经很稀少了，个别离群值可以忽略。中图反映了以[x,y]为坐标的点，基本上都会落在直线两侧。

如果X是均值不为零，且方差不为1的正态分布，此时对 [X, Y]进行绘图，数据点仍然会落在一条直线两侧，但这条直线不是$y=x$, 而是 

$$
y = \frac{x-\mu}{\sigma}
$$

上述直线方程，刚好也是z-score标准化方程：

$$
z = \frac{x - \mu}{\sigma}

```python
import numpy as np
import pandas as pd

def median(arr):
    idx = np.argsort(arr)
    half_idx = len(arr) // 2
    sx = idx[half_idx]

    if len(arr) == len(arr) // 2 * 2:
        ex = idx[half_idx - 1]
        return (arr[sx] + arr[ex]) / 2

    return arr[sx]

arr = np.array([35, 17, 9, 22, 1])

odd = median(arr)
print(f"odd-sized array: {odd}")
assert odd == np.median(arr)

arr = np.append(arr, [6])
even = median(arr)
print(f"even-sized array: {even}")
assert np.isclose(even, np.median(arr))
```

#### 1.1.3. 众数

众数是数列中出现次数最多的数字，当多个数字都出现最高次数时，多个数字都是众数。numpy中没有计算众数的方法，我们一般使用 `scipy.stats.mode` , `statistics.mode`[^statistics] 或者 `df.mode` 来计算。对离散变量，我们一定可以找出它的众数，但对连续随机变量，寻找众数不一定有意义，此时可用改用直方图来寻找分布最多的区间。

如果是出于绘图的目的（见 `skew_examples` )，众数的位置可以通过 `pdf` 最大值所处的位置来定位，毕竟，这就是 `pdf` 的物理意义所在。关于 `pdf` ，将在[频数、pmf、pdf...](#23-频数pmfpdfcdf和直方图)那一节中介绍。


### 1.2. 量化数据的分散程度

量化数据分散程度最简单的方法查看它的min, max和中位数，以及它们之间的距离。其中其最大值与最小值之间的差距又称为极差，这实际上是技术分析指标ATR[^ATR]的思想来源。不过，在这一节里，我们将介绍一些更加复杂的概念。

#### 1.2.1. 分位数

分位数亦称分位点，是指将一个随机变量的概率分布划分为几个等份的数值点，常用的有中位数（即二分位数）、四分位数、75%分位数等。我们可以使用 `np.quantile` 或者 `np.percentile` 来计算，在计算结果上两者没有区别。

我们也可以绘制简单的箱形图，来查看其分布。箱形图会显示最小值、25%分位、中位数、75%分位数和最大值：

```python
df = pd.DataFrame(data=arr)
props = dict(boxes="#B797D9", whiskers="#F2BEC7", 
            medians="#3C74A6", caps="#5BA582")
df.plot.box(color=props, patch_artist=True)
```
![100%](https://images.jieyu.ai/images/2023/06/nagwa_boxplot.png)
<figcaption>boxplot explained</figcaption>

这张图来自于这篇文章[^nagwa]。

现在，如果我们将问题中的价格波动区间绘制成箱形图，会不会有个初步的答案？

![100%](https://images.jieyu.ai/images/2023/06/box_and_candlestick.png)

从图中可以看出，显然中间的图，区间分布更为集中，适合做网格；而左右两图，都出现了较多离群值，说明波动过大，网格难以覆盖。

下面，我们将到目前为止，所有提到过的概念，通过图示的方式展现出来：

```python
from scipy import stats
import matplotlib.pyplot as plt
import numpy as np
from statistics import mode, median

np.random.seed(78)

# choose some parameters
a, loc, scale = 5.3, -0.1, 2.2

# draw a sample
data = stats.skewnorm(a, loc, scale).rvs(1000)

# estimate parameters from sample
ae, loce, scalee = stats.skewnorm.fit(data)

plt.figure()
plt.title("mean,median,percentile...")

plt.hist(data, bins=100, density=True, linewidth=1,
        alpha=0.6, fc='#EABFC7', ec='#EABFC7')

xmin, xmax = plt.xlim()
x = np.linspace(xmin, xmax, 100)
p = stats.skewnorm.pdf(x, ae, loce, scalee)

plt.plot(x, p, '#B297D5')

ymin, ymax = plt.ylim()

dmean = np.mean(data)
dmedian = median(data)
mode_pos = np.argmax(p)
mode_x = x[mode_pos]

x25 = stats.skewnorm.ppf(0.25, ae, loce, scalee)
x75 = stats.skewnorm.ppf(0.75, ae, loce, scalee)

# 在连续分布下，mode值没有意义，但其位置为pdf最大值处
plt.vlines(x=[mode_x], ymin=ymin, ymax=p[mode_pos], color='red')
plt.annotate(f"mode", xy=(mode_x, p[mode_pos]), color='red')

plt.vlines(x=[dmedian], ymin=ymin, ymax=0.4, color='#4A73A2')
plt.annotate(f"median {dmedian:.2f}", xy=(0.5, ymax * 0.9), color='#4A73A2')

plt.vlines(x=[dmean], ymin=ymin, ymax=0.4, color='#5BA582')
plt.annotate(f"mean {dmean:.2f}", xy=(dmean * 1.1, 0.4), color='#5BA582')

plt.vlines(x=[x25], ymin = ymin, ymax = ymax, color='#EEDD7C')
plt.annotate(f"25% {x25:.2f}", xy=(-0.5, ymax), color='#EEDD7C')

plt.vlines(x=[x75], ymin = ymin, ymax = ymax, color='#B297D5')
plt.annotate(f"75% {x75:.2f}", xy=(x75, ymax), color='#B297D5')

plt.show()
```

![75%](https://images.jieyu.ai/images/2023/06/put_it_all_together.png)

图中从左到右，依次为25%分位、众数、中位数（与50%分位重合）、均值和75%分位数。

这里我们用到了skewnorm这个模型，它有几个形态类参数，即`a`, `loc`和`scale`。关于这几个参数的命名和更进一步的讨论，可以查看这个链接[^scipy_stats_loc]。



#### 1.2.2. 方差和标准差

从测量的角度看，我们常常使用一组数据的均值来代表被测量属性的真实值，测量值与均值之间的差值就构成误差。方差和标准差本来是用来表示度量误差大小的方法。但这两个概念也可以延伸出来用以刻画数据本身的分散程度。

从概率统计的角度看，设总体$X$，变量数据为$N$，设$X_{i} (i=1, 2, ...,n)$为来自总体的样本，则有：

$$E(X) = \mu \tag{1.2.2a}$$
我们可通过`np.mean(X)`来计算，这个均值也被称为$X$的数学期望。

我们用方差`Variance`来描述总体中随机变量与其均值之间的偏离程度：
$$D(X) = \sigma^2 = E(X^2) - E^2(X) = \frac{\sum_{i=1}^{n}{(x_i-\mu)^2}}{N}\tag{1.2.b}$$

我们可以用`np.var`来计算方差。比如：

```
{code-block} python
>>> arr = np.arange(10)
>>> np.var(arr)
    8.25
```

方差的量纲与随机变量并不一致，所以我们更常使用标准差这个概念，它是通过对方差取开方得到的：
$$\sigma(X) = \sqrt\frac{\sum_{i=1}^{N}{(x_i-\mu)^2})}{N}\tag{1.2.2b}$$

我们可以通过 `np.std(X)` 来计算标准差。

```
{code-block} python
>>> arr = np.arange(10)
>>> np.std(arr)
    2.8722

>>> np.is_close(np.sqrt(np.var(arr)), np.std(arr), 10)
    True
```

上述方法计算出来的都是总体（population）均值、总体方差和总体标准差。

统计上还有一个样本均值、样本方差和样本标准差的概念，当我们谈及样本方差和标准差时，要注意公式是：

$$S^2 = \frac{1}{n-1}\sum_{i=1}^{n}(Y_i - \bar{Y})^2\tag{1.2.2c}$$
与总体方差的区别是，我们求平均值时，应该使用$n-1$作为分母，而不是$N$。

#### 1.2.3. 频数、PMF、PDF、CDF、PPF和直方图

即便有了上述概念，我们对数据分布的情况仍然没有全面掌握。我们仅仅是把握了它的几个关键点而已。

回想在网格交易法中，我们采用了一个给数据打格子的方法。直方图就是使用类似的方法，在一定的数据范围内，打上格子（bins），再把数据往这些格子里装，然后统计每个格子有多少数据，这个统计值就是频数(frequency count)。将频数、格子绘制成柱状图，就构成了直方图。

直方图除了可以直观地展示数据分布外，还可以判断数据是否存在离群值(outlier)。

下图显示了我们给 `data` 分组并统计频数，并在此基础上，计算概率、累积概率和概率密度的情况：

```python
from scipy import stats
import pandas as pd
import numpy as np

np.random.seed(78)

# choose some parameters
a, loc, scale = 5.3, -0.1, 2.2

# draw a sample
data = stats.skewnorm(a, loc, scale).rvs(1000)

ae, loce, scalee = stats.skewnorm.fit(data)
minx, maxx = np.min(data), np.max(data)
x = np.linspace(minx, maxx, 100)
p = stats.skewnorm.pdf(x, ae, loce, scalee)

n, edges = np.histogram(data)

df = pd. DataFrame({
    '频数<br>n': n,
    '累积频数 cumsum(n)': np.cumsum(n),
    '概率<br>PMF': n / np.sum(n),
    '累积概率<br>CDF': np.cumsum(n) / np.sum(n),
    '概率密度<br>PDF': (n/np.sum(n))/ np.diff(edges)
})

df.index = [f"[{s:.2f}-{e:.2f})" for s, e in zip(edges[:-1], edges[1:])]

style = df.style\
    .set_properties(**{'text-align': 'left'})\
    .set_table_styles(
        [dict(selector='th', props=[('text-align', 'left')])]
    )

colors = ['#5BA682', '#F2BEC7', '#B797D9', '#F2DC6A', '#3C74A6']
for col, color in zip(df.columns, colors):
    style = style.bar(subset=col, color=color)
    
style
```

显示结果如下：

![](https://images.jieyu.ai/images/2023/06/color_histo.png)

注意上图中最后三列的表头。这里的中文是正确的，而对应的英文，只在连续随机变量中才有效，而直方图表示的是各个区间对应的值--是离散变量。

#### 1.2.4. 概率密度和概率密度函数

在上图中，将每一个格子的频数除于总数( `len(data)` )，就得到了每个格子的**概率（PMF）**。

每个格子都有自己的上下界。上下界之差，构成了格距。每一行的概率除于格距，就构成了**概率密度(probability density)**。

$$
f_i = p_i/\Delta = n_i/n\Delta \tag{1.2.4a}
$$

当格距趋于无限小时，$f_i$就为**概率密度函数pdf**。

#### 1.2.5. 累积概率和累积概率函数CDF

在上图中，将每个格子的概率，从左到右加起来，直到当前的格子，就构成了当前格子的累积概率。在连续作用域下，概率密度函数的积分就成为CDF。CDF有以下特性，它永远大于零，并在正无穷处逼近1。

```python
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Polygon     
from scipy.stats import norm

np.random.seed(78)
rv = norm(loc=0)

x = np.linspace(rv.ppf(0.001), rv.ppf(0.999), 1000)

mu = rv.stats('m')
assert mu != np.mean(rv.rvs(size=1000))

pdf = norm.pdf(x)
cdf = norm.cdf(x)

fig, ax = plt.subplots(figsize = (7, 5))
plt.plot(x, pdf, color='#B297D5', label='sample pdf')
plt.plot(x, cdf, color='#4A73A2', alpha=0.6, label='sample cdf')

pos = np.argwhere(x <= rv.ppf(0.5)).flatten()[-1]

ix= x[:pos]
iy= pdf[:pos]

a, b = x[0], x[pos]

verts = [(a, 0), *zip(ix, iy), (b, 0)]
# 通过Polygon来绘制积分部分
poly = Polygon(verts, facecolor='0.7', edgecolor='0.5')
ax.add_patch(poly)       
 
ax.text(x[pos], pdf[pos], 
        f"{cdf[pos]:.2f} ({x[pos]:.2f}, {pdf[pos]:.2f})")

ax.plot(x[pos], cdf[pos], f"{cdf[pos]:.2f}", marker='.', 
        markerfacecolor='b', markersize=12)

plt.legend(['pdf', 'cdf'])
plt.show()
```

![75%](https://images.jieyu.ai/images/2023/06/cdf_and_pdf.png)

#### 1.2.6. CDF估计及其应用

在不知道概率分布特性的情况下，要获得cdf/pdf函数往往是困难的。但是，如果我们有足够多的随机变量取值，我们可以估计出它的cdf/pdf。

这里我们需要使用 `statsmodels` 这个库:
```
!pip install statsmodels
```

接下来，我们模拟一个双峰分布，然后来求它的经验累积分布函数(ECDF, emperical cumulative distribution function):

```python
from matplotlib import pyplot
from numpy.random import normal
from numpy import hstack
from statsmodels.distributions.empirical_distribution import ECDF

# generate a sample

sample1 = normal(loc=20, scale=5, size=300)
sample2 = normal(loc=40, scale=5, size=700)
sample = hstack((sample1, sample2))

# plot the histogram
pdf, edges, _ = plt.hist(sample, bins=100, density=True)

ecdf = ECDF(sample)
plt.plot(ecdf.x, ecdf.y)
plt.show()
```
![100%](https://images.jieyu.ai/images/2023/06/双峰.png)

#### 1.2.7. 几个概念之间的关系
PMF是概率质量函数的意思，它是离散作用域下的PDF。

PDF是CDF的导数，CDF是PDF的积分。二者之间的关系可如下推导：

```python
import scipy.stats as stats
import matplotlib.pyplot as plt
import numpy as np

# shape, loc, scale - creates weibull object
wei = stats.weibull_min(2, 0, 2) 
sample = wei.rvs(1000)
shape, loc, scale = stats.weibull_min.fit(sample, floc=0) 

x = np.linspace(np.min(sample), np.max(sample))
dx = x[1]-x[0]
deriv = np.diff(wei.cdf(x))/dx
plt.hist(sample, density=True, fc="none", 
        ec="grey", 
        label="frequency")

plt.plot(x, wei.cdf(x), label="cdf", color="#6DA485")
plt.plot(x, wei.pdf(x), label="pdf", color="#B297D5")
plt.plot(x[1:]-dx/2, deriv, '.', 
        label="derivative", color="#EABFC7")
plt.legend(loc=1)
plt.show()
```
![75%](https://images.jieyu.ai/images/2023/06/weibull_cdf_pdf.png)

上图中，原始`pdf`曲线为绿色，通过`cdf`微分出来的结果，我们以红色点来表示，可以看出，它们完全重合。

CDF的反函数(inverse function)是PPF。 `skewnorm.ppf(0.99, a, loc, scale)` 给出的是在以参数 `(a, loc, scale)` 定义的偏度分布中，99%分位数对应的数值。请看下面的变换：

```python
import numpy as np
from scipy.stats import norm

np.random.seed(78)
data = np.random.normal(size=100)

loc, scale = norm.fit(data)

# 求99.5%概率处对应的值，记为v, 2.59
v = norm.ppf(0.995, loc, scale)
print(v)

# 求`v = 2.59`处的累积概率，也为0.995
print(norm.cdf(v, loc, scale))
np.isclose(norm.cdf(v, loc, scale), 0.995)
```

!!! question
    假设现在是交易时间，沪指已经跌了4%。你应该有足够的知识来回答这个问题，沪指继续往下跌的概率是多少？如果我们希望以99%的获胜概率来抄底，那么应该等待沪指跌到多少时入场较好？

    我们把这个题留成作业题。在计算时，可以考虑取最近1000个交易日的沪指涨跌幅为原始数据。



```
在得到系数项之后，我们将它传给`np.poly1d`，就生成了一个新的多项式函数，当我们传入自变量`x`后，就生成了对应的$\hat{y}$。

结果显示如下图：

![](https://images.jieyu.ai/images/2023/06/fit_and_residual.png)

直观上看，上述数据更贴近某个二次曲线。现在，我们通过残差的概念，来检查一下，两次拟合的具体情况。
```python
from sklearn.metrics import max_error, mean_absolute_percentage_error, mean_squared_error

y = np.array([ x**2 -5 for x in range(-10, 10)]) + np.random.random(20) * 10

coff_1 = np.polyfit(x = np.arange(-10, 10), y = y, deg=1)
coff_2 = np.polyfit(x = np.arange(-10, 10), y = y, deg=2)

f1 = np.poly1d(coff_1)
f2 = np.poly1d(coff_2)

yhat2 = f2(np.arange(-10, 10))
yhat1 = f1(np.arange(-10, 10))

pos2 = np.argmax(abs(y - yhat2))
print(f"max error between yhat2 and y: {pos2}, {max_error(y, yhat2):.2f}")

pos1 = np.argmax(abs(y -yhat1))
print(f"max error between yhat1 and y: {pos1}, {max_error(y, yhat1):.2f}")

print(f"mape between yhat2 and y:{mean_absolute_percentage_error(y, yhat2):.2%}")
print(f"rmse between yhat2 and y:{mean_squared_error(y, yhat2, squared=False):.2f}")
```
在上述示例中，二次曲线拟合与原序列的 `max_error` 为5.24，发生在第0个元素上；一次曲线拟合与原序列的 `max_error` 为56.31，发生在第19个元素上。看起来二次曲线的拟合结果更好。

但是，拟合曲线与原曲线之间的相对误差达到了71.57%，标准差达到了2.76，这说明拟合曲线与原曲线实际上也不能较好地拟合。

!!! Attention
    在统计分析中，我们很可能愿意忽略个别的`max_error`，因为它们可能是分布上的所谓离群值。但在证券交易中，我们必须警惕这个`max_error`，如果我们按照拟合的函数来预测未来，当`max_error`真的出现时，可能会使得我们在某个加杠杆的操作中，一次性地被爆仓。


在k线图中，我们常常把一些重要的顶点连线起来，将它的延伸线作为压力线。反之，一些重要的底部的连线，我们将它的延伸线当成支撑线。这些都是线性回归的例子。

![](https://images.jieyu.ai/images/2023/08/lesson12-resist-line.png)

这个图，显示2023年8月3日，沪指的5日均线图。我们通过程序自动捕捉它的顶点，进行拟合连线，可以看出，有时候确实存在这样的压力线。这段期间还存在着一条支撑线，这里没有绘制出来。

一些长周期的均线往往能进行较好的一次曲线拟合。这对我们判断它的整体趋势是有益的。一般情况下，我们应该避开整体向下的标的，短周期的均线则有可能拟合成二次曲线。这对我们判断它的动量特征，发现短期是否可能反转有一定帮助。

## 3. 相关性

经济活动中往往具有关联性。比如，上游原材料涨价后，下游企业的利润就可能会受影响。这是我们从经济活动的原理推导出来的结论。如果从数据分析的角度，是否存在某种方法，可以揭示两组数据之间的关联？


这种方法如果存在，它在证券分析中的作用是不言而喻的。

比如，如果我们从历史数据中发现，美股中的某支证券与A股的某个板块有较强的关联度，那么如果近期该美股大幅上涨，那么可能迟早会引起A股某个板块上涨。

又比如，如果我们打算购买某支指数基金。评价指数基金的指标之一，是它能不能盯住指数。这也是一个相关性问题。

实际上，利用两个相关性较高的证券品种进行配对交易，卖出相对高估的品种，买入相对低估的品种，这种交易策略被称之为配对交易，是一种主流的量化中性策略。

!!! Note
    关于配对交易，涉及到平稳性检验和协整理论，这里不深入介绍。协整性可以通过statsmodels中的`conint`来计算。


### 3.1. 协方差和相关系数

协方差(covariance)描述的是随机变量联合变化程度。通俗一点讲，假设股票B是板块A中一支股票，那么当板块A上涨时，B也可能上涨；当板块A下跌时，B也可能下跌，这就是联合变化。协方差就是以量化的方式来定量分析这种联合变化的程度。

假设板块A每日涨跌幅记为$X$，股票B的每日涨跌记为$Y$，则两者的$n$日协方差为：

$$ cov(X, Y) = \frac{1}{n-1}\sum_{i=1}^n(X_i - \mu_X)(Y_i - \mu_Y) \tag 3
$$

上述方法计算出来的$cov$的大小，跟$X$,$Y$的大小相关。为了无量纲化，要对其进行标准化，就有了相关系数的概念：

$$\rho_{XY} = \frac{cov(X, Y)}{\sigma_X\sigma_Y}$$

!!! Note
    我们可以使用`np.cov`来计算协方差，使用`np.corrcoef`来计算相关系数。在pandas中，对应的分别是`df.cov`和`df.corr`。


下面，我们就选取一个板块和其中的几支股票，求一下它们的相关系数，并绘制成热力图：
```python
import matplotlib.pyplot as plt

codes = ["300607.XSHE", "300165.XSHE", "300535.XSHE", "603392.XSHG", "603718.XSHG", "002030.XSHE"]

start = datetime.date(2022, 9, 1)  # 起始时间， 可修改
end = datetime.date(2023, 3, 1)  # 截止时间， 可修改

board = "300941" #抗原检测

bbars = await Board.get_bars_in_range(board, start, end)

data = [ bbars["close"][1:]/bbars["close"][-1:] - 1]
names = [board]

for code in codes:
    bars = await Stock.get_bars_in_range(code, FrameType.DAY, start, end)
    
    pc = bars["close"][1:] / bars["close"][:-1] - 1
    data.append(pc)
    names.append(code.split(".")[0])


f = plt.figure(figsize=(19, 15))
plt.matshow(np.corrcoef(data), fignum = f.number)

plt.xticks(range(len(names)), names, fontsize=14, rotation=45)
plt.yticks(range(len(names)), names, fontsize=14)
cb = plt.colorbar()
cb.ax.tick_params(labelsize=14)
plt.title('Correlation Matrix', fontsize=16);
```
![50%](https://images.jieyu.ai/images/2023/06/corr_heatmap.png?1)

从热力图可以看出，板块指数（300941）与300535相关性很强，而与603392相关性则较弱。

我们把股价走势绘制成下图，显然，相关性系数完全反映了股价的走势对比：

![100%](https://images.jieyu.ai/images/2023/06/stock_trend_compare.png?1)

!!! Attention
    注意在热力图中，轴的取值区间是[0,1]。这是因为我们使用的`matshow`方法对取值进行了rescale。实际上，`np.corrcoef`的输出值范围是[-1, 1]。相关系数越接近-1，表明走势越相反；越接近1，表明走势越接近。如果相关性为0，则表明两者是正交关系。

    在因子选择时，我们应该选择相关系数接近于0的因子，每个因子都能独立对收益有贡献；在进行资产组合投资时，应该选择资产收益相关性接近零的品种，以分散风险。


上述热力图对应的相关系数为：

![50%](https://images.jieyu.ai/images/2023/06/corr_values.png)

### 3.2. 皮尔逊相关性和斯皮尔曼相关性

我们刚刚计算的相关系数，实际上叫皮尔逊(pearson)相关系数。

皮尔逊相关性的适用条件是，两个随机变量都要服从正态分布，数据至少在逻辑范围内是等距的。由于金融数据分布往往只是近似于正态分布，所以在出现尾部风险时，这种相关性计算方法就不能用了。

另外，有一些场合，数据取值并不是等距的。比如，近期收益回报前3的股票，第1名与第2名的收益差与第2名和第3名的收益差往往是不相等的。这种情况下，也不满足皮尔逊相关性条件，这就需要使用斯皮尔曼相关系数。

Spearman是一种秩相关，它是一个非参数性质（与分布无关）的秩统计参数，是用来度量两个连续型变量之间单调关系强弱的相关系数，取值范围也是 [−1,1]。在没有重复数据的情况下，如果一个变量是另外一个变量的严格单调函数，则 Spearman 秩相关系数就是 1 或 −1，称变量完全 Spearman 秩相关。

在因子分析中，现在越来越倾向于认为RANK-IC的鲁棒性更好，因此因子分析中Spearman相关更常用。

$$
 r_s =
 \rho_{\operatorname{R}(X),\operatorname{R}(Y)} =
 \frac{\operatorname{cov}(\operatorname{R}(X), \operatorname{R}(Y))}
      {\sigma_{\operatorname{R}(X)} \sigma_{\operatorname{R}(Y)}} \tag 4
$$

这里的秩相关 (Rank Correlation)，又称等级相关，是将两变量的样本值按数据的大小顺序排列位次，以各要素样本值的位次代替实际数据而求得的一种统计量。排序不论从大到小还是从小到大排都无所谓，只要保证大家排序的标准一致即可。


下面，我们仍以上面的板块和股票为例，来计算Spearman相关系数：
```python
from scipy import stats

corr, pvalue = stats.spearmanr(data, axis=1)

f = plt.figure(figsize=(9, 7))
plt.matshow(corr, fignum = f.number)

plt.xticks(range(len(names)), names, fontsize=14, rotation=45)
plt.yticks(range(len(names)), names, fontsize=14)
cb = plt.colorbar()
cb.ax.tick_params(labelsize=14)
plt.title('Correlation Matrix', fontsize=16);
```
生成的热力图与使用pearson的相差不大，这里就不展示了。

!!! Tip
    在numpy中没有计算皮尔逊相关性的函数。这次我们将使用`scipy.stats.spearmanr`来计算相关性。如果数据已经是DataFrame格式，也可以使用`df.corr(method='?')`来计算。这里`method`可以是空（pearson)，或者`spearman`和`kendall`。


`stats.spearmanr`方法的签名如下：
```
{code-block}python
stats.spearmanr(
    a,
    b = None,
    axis = 0,
    nan_policy = 'propagate',
    alternative = 'two-sided'
)
```
`a`和`b`是待检验相关性的数据。如果`a`和`b`是二维的数组，则`axis`有意义。在示例中，由于我们的观察数据是按列存放的（即每一行是一组数据），所以这里指定`axis=1`。在`b`没有指定的情况下，相关性将在`a`和`a.T`之间进行计算。

`spearmanr`方法返回相关性系数矩阵和`pvalue`。

!!! attention
    `spearmanr`的原假设（null hypothesis）是两组数据是**非线性相关**。因此，当p值小于5%时，我们拒绝原假设，即两组数据是线性相关的。

    `spearmanr`要求观察数据至少在50个以上。我们的示例中，观察值是118个，满足条件。如果观察数据不足50，scipy的建议是使用`stats.permutation_test`。


### 3.3. 相关性分析示例

我们来做这样一个实验。取一支股票，获取它的每日收盘价，分别计算以下指标：

1. 每日涨跌比率，即: $close[1:] / close[:-1] - 1$
2. 移动5日收益率，即 $close[5:] / close[:-5] - 1$
3. 移动10日收益率，即 $close[10:] / close[:-10] - 1$
4. RSI，窗口为6
5. RSI的导数，即 $rsi[1:] - rsi[:-1]

我们分别取前一日的2~5的数据，生成`DataFrame`，再取每日涨跌比率，生成`pd.Series`，最终计算1和其它各项的相关系数：
```python
code = '603392.XSHG'
start = datetime.date(2023, 1, 1)
end = datetime.date(2023, 3, 1)
bars = await Stock.get_bars_in_range(code, FrameType.DAY, start, end)

close = bars["close"].astype(np.float64)
rsi = ta.RSI(close, 6)

pct1 = (close[1:]/close[:-1] - 1)

data = {}
for n in (5, 10):
    data[f"pct{n}"] = (close[n:]/close[:-n] - 1)[-21:-1] * 10

data["pre_rsi"] = rsi[-21:-1]
data["rsi"] = rsi[-20:]

drsi = np.diff(rsi)
data["pre_drsi"] = drsi[-21:-1]
data["drsi"] = drsi[-20:]

df = pd.DataFrame(data)
s = pd.Series(pct1)

df.corrwith(s)
```
!!! Tip
    相关系数是量纲无关的。读者可以把第8行的`rsi`归一化到区间[0, 1]间再试试，输出的结果将会是一致的。


最终输出结果如下：
```
pct5        0.028
pct10       0.215
pre_rsi     0.36
rsi        -0.029
pre_drsi    0.149
drsi       -0.34
```
数据表明，当日涨跌与前一日的`rsi`的相关系数是0.36，和前一日`drsi`的相关系数是-0.34，有观点认为0.3以上的系数也可以认为存在弱相关；但与近5日收益率、10日收益率的相关性比较弱。

如果我们将当日涨跌与当日`drsi`对照绘图，会发现它们的走势几乎完全一致，这不难理解，毕竟涨跌影响着RSI的取值。

但如果用前一天的drsi，即pre_drsi与pct1对照绘图，它们的走势也极为相似，但有一天的相位差。也就是前一日的rsi的涨跌，会影响到次日股价涨跌。

![75%](https://images.jieyu.ai/images/2023/06/pct_drsi.png)

也就是说，pct1与drsi之间，pct1与pre_drsi之间都存在某种关联。不管这种关联有没有实际利用价值，但是我们在这里发现了一个现象，即存在关联的两个序列，并不一定能通过相关性分析给找出来。

那么这种情况下，我们又应该通过什么方法来找出它们之间的联系呢？

这就引入了相似性的概念。

## 4. 距离和相似性

判断两组数据是否相似，需要先定义度量方法。

相似性度量方法在多元统计中的聚类分析、机器学习等方面都有应用。随着机器学习在证券分析中的应用越来越广泛，我们也有必要介绍一些入门知识。

相似性是通过距离来度量的。如果两组数据之间的距离越大，那么相似性越小；反之，相似性越大，那么距离越小。

!!! Tip
    一般而言，定义一个距离函数 d(x, y), 需要满足下面几个准则：

    1. 到自己的距离为0，即：$$d(x,x) = 0$$           
    2. 距离非负，$$d(x,y) >= 0$$
    3. 对称性，即对称性: 如果 A 到 B 距离是 a，那么 B 到 A 的距离也应该是 a:
    $$
    d(x, y) = d(y, x)             
    $$
    1. 三角形法则: (两边之和大于第三边)
    $$
    d(x, k)+ d(k, y) >= d(x, y)
    $$

形法则: (两边之和大于第三边)
 <br><br>通过求两个向量之间的夹角来定义其相似度。如果两个向量完全相似，则夹角为零。早期在计算文本相似度上有较多应用。
    $$ S = \frac{x.y}{|x||y|} \tag 6$$

3. **马氏距离**
    <br><br>马氏距离是基于样本分布的一种距离。

4. **编辑距离**
    <br><br>是指两个字串之间，由一个转换成另一个所需的最少编辑操作次数；在软件中常用此方法来查找文章中的拼写错误。如果一个词不在词库里，但又与词库中的某个词在编辑距离上很接近，则可能判断这里出现了拼写错误。

5. **杰卡德距离**
    <br><br>用来判断两个集合之间的距离。可用以推荐算法中。

6.  **皮尔逊相关系数**
   <br><br>形式上类似于余弦相似性。

### 4.2. 如何计算距离
`scipy.spatial.distance`包和`sklearn.metrics.pairwise`都提供了距离计算的工具。

下面，我们就以以下三条线为例，展示如何计算它们之间的距离：

```python
import numpy as np
import matplotlib.pyplot as plt
from scipy import stats

x=np.arange(0,np. pi*2, 0.1)
y1=np.sin(x)
y2=np.cos(x)-2
y3=y1-2

plt.plot(y1)
plt.plot(y2)
plt.plot(y3)
plt.legend(['y1','y2','y3'])
```

![50%](https://images.jieyu.ai/images/2023/06/three_sim_lines.png)

从视觉上看，我们一般认为`y1`, `y2`和`y3`都是相似的曲线。y3相当于y1的缩小版本，y2则是在y1的基础上进行了平移。如果我们还有曲线`y4`，假设它是`y1`在平面上进行了90度旋转，我们一般也认为`y4`和`y1`相似，这一点在图像处理领域尤其如此。比如，一张泰迪熊的照片，无论是横着放、还是竖着放，都不会改变它是一张泰迪熊的事实。

```python
from sklearn.metrics.pairwise import euclidean_distances, cosine_distances, cosine_similarity
from scipy.spatial.distance import euclidean, cosine
import pandas as pd

def corr(x1, x2):
    return round(np.corrcoef(x1, x2).flatten()[1], 2)

def calc_dist(label, x1, x2):
    return [
        label,
        round(euclidean_distances([x1], [x2]).flatten()[0], 2),
        round(cosine_distances([x1], [x2]).flatten()[0], 2),
        round(cosine_similarity([x1], [x2]).flatten()[0], 2),
        corr(x1, x2)]

data = []
data.append(calc_dist("y1->y2", y1, y2))
data.append(calc_dist("y2->y3", y2, y3))
data.append(calc_dist("y1->y3", y1, y3))

df = pd.DataFrame(data, columns="label,eucl,cos_dis,cos_sim,corr".split(","))
df
```
这段代码分别两两计算了`y1,y2,y3`之间的欧氏距离、余弦距离、余弦相似度和皮尔逊距离。余弦距离和余弦相似度几乎完全相同。用1减去余弦距离，就得到了余弦相似度。

输出结果如下：

![50%](https://images.jieyu.ai/images/2023/06/dist_results.png)

从欧氏距离和余弦距离（相似度）的角度看，都是y2和y3最接近；但从相关性上看，y1和y3相关性很强，但其它两对关系中则不相关。总的来说，我们使用的这些相似性检测算法，没有一个能够完全反映出三条曲线实际是都是相似的这一结论。

这个结果反映了我们要找出一组证券中，具有相同特点的证券的难度。如果仅仅是要检测股价走势的相似性，可以考虑`fastdtw`[^fastdtw]方法。

## 5. 归一化

归一化是机器学习中，预处理的重要步骤。在因子分析中也常常用到。这里我们简单讨论归一化数学函数。更多关于归一化（或者更泛化地讲，缩放甚至预处理，可参见sklearn.preprocessing包）。

在[第9课 Numpy和Pandas](chap03-%E7%AC%AC9%E8%AF%BE.md)那一节中，有一个习题，求当日的成交量是多少个周期以来的地量。这道题的结果，理论上是 $[1, +\infin]$ 。但在机器学习中，或者任何其它需要统一量纲的场合，我们都需要一种方法，将其进行缩放，使之被压缩到一个狭窄的小区间，同时保持单调性不变。

一般情况下，我们可以通过sigmoid函数（又称为logistic）来完成这个转换。sigmoid函数的公式如下：
$$
    S = \frac{1}{1 + e^{-x}}
$$

当x趋向$-\infin$时，$S$取值趋向0，当x取值趋向$+\infin$时，$S$取值趋向1；当x为零时，取值为0.5，在0.5附近,$S$对X的变化更为敏感。

另外一个常用的函数是tanh。我们将两者的变换图对比如下：

![50%](https://images.jieyu.ai/images/2023/06/comarison_tanh_sigmoid.png)

两者的主要区别在函数值域上。sigmoid取值为(0, 1)，而tanh的取值为(-1,1)。另外一个不同之处是，在中心区，似乎tanh上升得更快，不过这些都可以通过变换来消除。

另一个比较重要的区别就是在机器学习中，出于性能的考虑，更多时候我们使用sigmoid函数而不是tanh，这是因为对sigmoid函数求导的计算存在简便算法，因此会快很多。

直接使用sigmoid函数仍会有很多不便之处。比如，假设有随机变量的值域是$(-\infin, +\infin)$，但90%以上的取值分布在[10,30]之间（比如当我们寻找圆弧底时，其宽度的最小值为3，最大不限，但我们倾向寻找10\~30的宽度，这个区间能更好地体现筹码博弈，由下跌到止跌回升的过程。这样我们就倾向于让10\~30处的数值，在归一化时，有最好的响应灵敏度）。

因此，我们希望sigmoid在这一区域，有更好的响应灵敏度。但是，如果我们直接使用sigmoid函数，结果如下：

```python
import matplotlib.pyplot as plt

def sigmoid(x):
    return 1/(1 + np.exp(-x))

x = np.linspace(10, 30, 20)
plt.plot(x, [sigmoid(i) for i in x])
```
输出结果如下：

![50%](https://images.jieyu.ai/images/2023/06/sigmoid_no_tuned.png)

这个结果很难说理想。事实上，从x = 12开始，它们的sigmoid值就不再有区分度。

从下面的例子可以看出，尽管从数学的角度，sigmoid(12)和sigmoid(13)是不相等的，但在实际的运用中，一般认为它们是相等的：

```python
def sigmoid(x):
    return 1/(1 + np.exp(-x))

print("sigmoid(12) == sigmoid(13)?", np.isclose(sigmoid(12), sigmoid(13)))
```
所以，如果我们使用原始的sigmoid来进行归一化，12个周期以来的地量与200个周期以来的地量将没有任何区别，但实际上，后者出现的频率很低，一旦出现，它的信号意义将很强。

我们给出如下的方法，以对sigmoid函数进行调整，使之能在我们期望的区间内，有较好的响应灵敏度：
```python
import matplotlib.pyplot as plt

def scaled_sigmoid(x, start, end):
    """当`x`落在`[start,end]`区间时，函数值为[0,1]且在该区间有较好的响应灵敏度
    """
    n = np.abs(start - end)

    score = 2/(1 + np.exp(-np.log(40_000)*(x - start - n)/n + np.log(5e-3)))
    return score/2


fig, (ax1, ax2, ax3,ax4) = plt.subplots(nrows = 1, ncols = 4, figsize=(12,3))

x = np.linspace(0, 1)
ax1.plot(x, [scaled_sigmoid(i, x[0], x[-1]) for i in x])
ax1.set_title("fit (0,1)")

x = np.linspace(0, 100)
ax2.plot(x, [scaled_sigmoid(i, x[0], x[-1]) for i in x])
ax2.set_title("fit (0, 100)")

x = np.linspace(18, 38)
ax3.plot(x, [scaled_sigmoid(i, x[0], x[-1]) for i in x])
ax3.set_title("fit (18, 38)")

x = np.linspace(0, 100)
ax4.plot(x, [sigmoid(i) for i in x])
ax4.set_title("fit (0,100) with original")
```
下图显示了经过调整后的sigmoid图在各个区间的响应情况，以及和未调整的sigmoid的对照：

![](https://images.jieyu.ai/images/2023/06/scaled_sigmoid.png)

从图中可以看出，无论`x`的值域在哪一个范围，调用后的sigmoid函数都能在该区间给出非常好的区分度。

[^fastdtw]: https://github.com/slaypni/fastdtw

本章结束后，我们基本上就掌握了必备的统计学知识。下图是我们介绍过的知识点，与wiki词条覆盖率对照：

![](https://images.jieyu.ai/images/2023/06/statistics_overview.png)

