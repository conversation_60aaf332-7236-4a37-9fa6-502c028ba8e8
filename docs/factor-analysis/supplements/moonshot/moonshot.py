import datetime
import sys
import time
from pathlib import Path
from typing import Dict, List, Literal, Optional, Union

import numpy as np
import pandas as pd
import polars as pl
import quantstats as qs

# 添加startup.py所在路径到sys.path
startup_paths = str(Path("~/workspace/cheese_course/docs/factor-analysis/assets").expanduser())
sys.path.append(startup_paths)


from startup import get_calendar, load_bars_tushare_all, pro_api

data_home = Path("/tmp/THSHistoryData")

class Moonshot:
    """量化回测框架类
    
    实现股票筛选、回测和报告功能
    """
    
    def __init__(self, daily_bars:pd.DataFrame):
        """初始化Moonshot实例
        
        Args:
            daily_bars: 列字段包含date, asset以及open, close的已复权数据，其中date必须为datetime.date类型
        """
        self.data: pd.DataFrame = self.resample_to_month(daily_bars, open='first', close='last')
        self.data['flag'] = 0

        # 加载股息率数据
        self.load_dividend_yield()

        self.strategy_returns: Optional[pd.Series] = None
        self.benchmark_returns: Optional[pd.Series] = None
    def resample_to_month(self, data: pd.DataFrame, **kwargs)->pd.DataFrame:
        """
        按月重采样，支持任意列的聚合方式

        Example:
            >>> resample_to_month(data, close='last', high='max', low='min', open='first', volume='sum')
        
        参数:
            data: DataFrame，需包含'date'和'asset'列。数据不要求有序。
            **kwargs: 关键字参数，格式为"列名=聚合方式"
                    支持的聚合方式：'first'（首个值）、'last'（最后一个值）、
                                    'mean'（平均值）、'max'（最大值）、'min'（最小值）
        
        返回:
            重采样后的Polars DataFrame
        """
        df = pl.from_pandas(data)
        df = df.with_columns(pl.col('date').cast(pl.Datetime))
        
        df = df.with_columns(
            pl.concat_str(
                [
                    pl.col('date').dt.year().cast(pl.Utf8),
                    pl.lit('-'),
                    pl.col('date').dt.month().cast(pl.Utf8).str.pad_start(2, fill_char='0')
                ]
            ).alias('month')
        )
        
        # 定义支持的聚合方式映射（列名 -> 聚合表达式）
        agg_methods = {
            'first': lambda col: col.sort_by(pl.col('date')).first(),
            'last': lambda col: col.sort_by(pl.col('date')).last(),
            'mean': lambda col: col.mean(),
            'max': lambda col: col.max(),
            'min': lambda col: col.min(),
            'sum': lambda col: col.sum()
        }
        
        # 构建聚合表达式列表
        agg_exprs = []
        for col_name, method in kwargs.items():
            if col_name not in df.columns:
                raise ValueError(f"数据中不存在列: {col_name}")
            
            # 检查聚合方式是否支持
            if method not in agg_methods:
                raise ValueError(f"不支持的聚合方式: {method}，支持的方式为: {list(agg_methods.keys())}")
            
            # 添加聚合表达式
            agg_exprs.append(
                agg_methods[method](pl.col(col_name)).alias(col_name)
            )
        
        if not agg_exprs:
            raise ValueError("至少需要指定一个列的聚合方式（如open='first'）")
        
        result = df.group_by(
            pl.col('asset'),
            pl.col('month')
        ).agg(agg_exprs).sort(pl.col('month'), pl.col('asset'))
        
        result = result.to_pandas()
        result['month'] = pd.PeriodIndex(result['month'], freq='M')

        return result.set_index(['month', 'asset'])
        
    def append_factor(self, data: pd.DataFrame, factor_col: str, resample_method: str|None=None) -> None:
        """将因子数据添加到回测数据(即self.data)中。

        如果resample_method参数不为None, 则需要重采样为月频，并且使用resample_method指定的方法。否则，认为因子已经是月频的，将直接添加到回测数据中。

        使用本方法，一次只能添加一个因子。

        Args:
            data: 因子数据，需包含'date'和'asset'列
            factor_col: 因子列名
            resample_method: 如果需要对因子重采样，此列为重采样方法。
        """
        # 检查必需的列是否存在
        if 'date' not in data.columns:
            raise ValueError("因子数据中不存在'date'列")
        if 'asset' not in data.columns:
            raise ValueError("因子数据中不存在'asset'列")
        if factor_col not in data.columns:
            raise ValueError(f"因子数据中不存在列: {factor_col}")

        if resample_method is not None:
            factor_data = self.resample_to_month(data, **{factor_col: resample_method})
        else:
            data_copy = data.copy()

            # 确保date列是datetime类型
            if not pd.api.types.is_datetime64_any_dtype(data_copy['date']):
                data_copy['date'] = pd.to_datetime(data_copy['date'])

            data_copy['month'] = data_copy['date'].dt.to_period('M')

            # 检查是否有重复的(month, asset)组合
            duplicates = data_copy.duplicated(subset=['month', 'asset'])
            if duplicates.any():
                duplicate_count = duplicates.sum()
                raise ValueError(
                    f"发现 {duplicate_count} 个重复的(month, asset)组合。"
                    "当resample_method=None时，传入的数据必须是无重复的月度数据。"
                    "如果您的数据是日频或有重复记录，请指定resample_method参数，"
                    "如：resample_method='last'、'mean'、'first'等"
                )

            factor_data = data_copy.set_index(['month', 'asset'])[[factor_col]]

        self.data = self.data.join(factor_data, how='left')
    
    def load_dividend_yield(self) -> None:
        """从本地parquet文件加载股息率数据并添加到self.data中
        
        数据来源：data_home / "Dividenddata" / "THSDividenddata" / "Dividend.parquet"
        """
        dividend_file = data_home / "Dividenddata/THSDividenddata/Dividend.parquet"
        
        if not dividend_file.exists():
            raise FileNotFoundError(f"警告：股息率数据文件不存在: {dividend_file}")
                    
        # 读取parquet文件
        dividend_data = pd.read_parquet(dividend_file)
    
        dividend_data = dividend_data.rename(columns={'time': 'date', 'ts_code': 'asset'})
        
        if 'dividend_rate_ttm' not in dividend_data.columns:
            raise ValueError("警告：未找到dividend_rate_ttm列")
                    
        self.append_factor(dividend_data, 'dividend_rate_ttm', resample_method='last')
        
        # 重命名列为标准的dividend_yield
        self.data = self.data.rename(columns={'dividend_rate_ttm': 'dividend_yield'})
    
    def screen(self, screen_method, **kwargs) -> 'Moonshot':
        """应用股票筛选器

        Args:
            screen_method: 筛选方法（可调用对象）
            **kwargs: 筛选器参数

        Returns:
            Moonshot: 返回自身以支持链式调用
        """
        if self.data is None or self.data.empty:
            raise ValueError("警告：数据为空，无法应用筛选器")

        if callable(screen_method):
            screen_method(**kwargs)
        else:
            raise ValueError("screen_method 必须是可调用对象")

        return self
    
    def dividend_yield_screen(self, top_n: int = 500) -> None:
        """股息率前N名筛选器

        Args:
            top_n: 选择前N名股票
        """
        if 'dividend_yield' not in self.data.columns:
            raise ValueError("警告：数据中不存在dividend_yield列，无法应用筛选器")

        # 使用groupby消除循环，对每个月份分组处理
        def select_top_stocks(group):
            """为每个月份选择前top_n名股票"""
            if group['dividend_yield'].isna().all():
                return group

            # 按股息率排序，选择前top_n名
            top_indices = group['dividend_yield'].nlargest(top_n).index
            group.loc[top_indices, 'flag'] = 1
            return group

        # 按月份分组应用筛选
        self.data = self.data.groupby(level='month').apply(select_top_stocks)
    
    def calculate_returns(self):
        """计算策略收益率
        """
        if self.data is None or self.data.empty:
            print("警告：数据为空，无法计算收益率")
            return self
                    
        strategy_returns = []
        
        for month in sorted(self.data.index.get_level_values('month').unique()):
            month_data = self.data.loc[month]
            
            # 获取当月被选中的股票
            selected_stocks = month_data[month_data.get('flag', 0) == 1]
            
            if len(selected_stocks) > 0:
                # 计算月收益率 (close - open) / open
                monthly_returns = (selected_stocks['close'] - selected_stocks['open']) / selected_stocks['open']
                
                # 等权重平均
                strategy_return = monthly_returns.mean()
                strategy_returns.append((month, strategy_return))
                
                print(f"{month.strftime('%Y-%m')}: 策略收益率 {strategy_return:.4f}, 选中股票数 {len(selected_stocks)}")
            else:
                strategy_returns.append((month, 0.0))
                print(f"{month.strftime('%Y-%m')}: 无选中股票")
        
        if strategy_returns:
            self.strategy_returns = pd.Series(
                [ret for _, ret in strategy_returns],
                index=[month for month, _ in strategy_returns],
                name='strategy_returns'
            )
        
        return self
    
    def calculate_benchmark(self) -> 'Moonshot':
        """计算基准收益率（所有股票等权持仓）
        
        Returns:
            Moonshot: 返回自身以支持链式调用
        """
        if self.data is None or self.data.empty:
            print("警告：数据为空，无法计算基准收益率")
            return self
            
        print("计算基准收益率...")
        
        benchmark_returns = []
        
        for month in sorted(self.data.index.get_level_values('month').unique()):
            month_data = self.data.loc[month]
            
            # 计算所有股票的月收益率
            monthly_returns = (month_data['close'] - month_data['open']) / month_data['open']
            
            # 等权重平均
            benchmark_return = monthly_returns.mean()
            benchmark_returns.append((month, benchmark_return))
            
            print(f"{month.strftime('%Y-%m')}: 基准收益率 {benchmark_return:.4f}, 股票数 {len(month_data)}")
        
        if benchmark_returns:
            self.benchmark_returns = pd.Series(
                [ret for _, ret in benchmark_returns],
                index=[month for month, _ in benchmark_returns],
                name='benchmark_returns'
            )
        
        return self
    
    def report(self) -> Dict:
        """生成回测报告
        
        Returns:
            Dict: 包含基本指标的报告
        """
        if self.strategy_returns is None or self.benchmark_returns is None:
            print("警告：收益率数据为空，请先计算收益率")
            return {}
            
        print("\n=== 回测报告 ===")
        
        # 基本统计指标
        strategy_stats = {
            '策略年化收益率': self.strategy_returns.mean() * 12,
            '策略年化波动率': self.strategy_returns.std() * np.sqrt(12),
            '策略夏普比率': (self.strategy_returns.mean() * 12) / (self.strategy_returns.std() * np.sqrt(12)),
            '策略最大回撤': self._calculate_max_drawdown(self.strategy_returns),
        }
        
        benchmark_stats = {
            '基准年化收益率': self.benchmark_returns.mean() * 12,
            '基准年化波动率': self.benchmark_returns.std() * np.sqrt(12),
            '基准夏普比率': (self.benchmark_returns.mean() * 12) / (self.benchmark_returns.std() * np.sqrt(12)),
            '基准最大回撤': self._calculate_max_drawdown(self.benchmark_returns),
        }
        
        # 打印报告
        print("\n策略表现:")
        for key, value in strategy_stats.items():
            print(f"{key}: {value:.4f}")
            
        print("\n基准表现:")
        for key, value in benchmark_stats.items():
            print(f"{key}: {value:.4f}")
            
        # 超额收益
        excess_return = strategy_stats['策略年化收益率'] - benchmark_stats['基准年化收益率']
        print(f"\n年化超额收益率: {excess_return:.4f}")
        
        return {
            'strategy': strategy_stats,
            'benchmark': benchmark_stats,
            'excess_return': excess_return
        }
    
    def _calculate_max_drawdown(self, returns: pd.Series) -> float:
        """计算最大回撤
        
        Args:
            returns: 收益率序列
            
        Returns:
            float: 最大回撤
        """
        cumulative = (1 + returns).cumprod()
        running_max = cumulative.expanding().max()
        drawdown = (cumulative - running_max) / running_max
        return drawdown.min()


if __name__ == '__main__':
    # 示例用法
    from datetime import datetime

    # 加载数据
    start_date = datetime.strptime('2023-01-01', '%Y-%m-%d').date()
    end_date = datetime.strptime('2023-03-31', '%Y-%m-%d').date()
    assets = ['000001.SZ', '000002.SZ', '600000.SH', '600036.SH', '000858.SZ']
    
    daily_bars = load_bars_tushare_all(start_date, end_date, assets)
    # 重置索引，将date和asset从索引转为普通列
    daily_bars = daily_bars.reset_index()
    moonshot = Moonshot(daily_bars)
    
    # 添加股息率筛选器
    moonshot.screen(moonshot.dividend_yield_screen, top_n=500)
    
    # 计算收益率
    moonshot.calculate_returns()
    moonshot.calculate_benchmark()
    
    # 生成报告
    report = moonshot.report()
    print(report)
