import datetime
import sys
import time
from pathlib import Path
from typing import Dict, List, Literal, Optional, Union

import numpy as np
import pandas as pd
import polars as pl
import quantstats as qs

# 添加startup.py所在路径到sys.path
sys.path.append(str(Path("~/workspace/cheese_course/docs/factor-analysis/assets").expanduser()))

from startup import get_calendar, load_bars_tushare_all, pro_api


class Moonshot:
    """量化回测框架类
    
    实现股票筛选、回测和报告功能
    """
    
    def __init__(self, daily_bars:pd.DataFrame):
        """初始化Moonshot实例
        
        Args:
            daily_bars: 列字段包含date, asset以及open, close的已复权数据，其中date必须为datetime.date类型
        """
        self.data = self.resample_to_month(daily_bars, open='first', close='last')

        self.strategy_returns: Optional[pd.Series] = None
        self.benchmark_returns: Optional[pd.Series] = None
    def resample_to_month(self, data: pd.DataFrame, **kwargs)->pd.DataFrame:
        """
        按月重采样，支持任意列的聚合方式

        Example:
            >>> resample_to_month(data, close='last', high='max', low='min', open='first', volume='sum')
        
        参数:
            data: DataFrame，需包含'date'和'asset'列。数据不要求有序。
            **kwargs: 关键字参数，格式为"列名=聚合方式"
                    支持的聚合方式：'first'（首个值）、'last'（最后一个值）、
                                    'mean'（平均值）、'max'（最大值）、'min'（最小值）
        
        返回:
            重采样后的Polars DataFrame
        """
        df = pl.from_pandas(data)
        df = df.with_columns(pl.col('date').cast(pl.Datetime))
        
        df = df.with_columns(
            pl.concat_str(
                [
                    pl.col('date').dt.year().cast(pl.Utf8),
                    pl.lit('-'),
                    pl.col('date').dt.month().cast(pl.Utf8).str.pad_start(2, fill_char='0')
                ]
            ).alias('month')
        )
        
        # 定义支持的聚合方式映射（列名 -> 聚合表达式）
        agg_methods = {
            'first': lambda col: col.sort_by(pl.col('date')).first(),
            'last': lambda col: col.sort_by(pl.col('date')).last(),
            'mean': lambda col: col.mean(),
            'max': lambda col: col.max(),
            'min': lambda col: col.min(),
            'sum': lambda col: col.sum()
        }
        
        # 构建聚合表达式列表
        agg_exprs = []
        for col_name, method in kwargs.items():
            if col_name not in df.columns:
                raise ValueError(f"数据中不存在列: {col_name}")
            
            # 检查聚合方式是否支持
            if method not in agg_methods:
                raise ValueError(f"不支持的聚合方式: {method}，支持的方式为: {list(agg_methods.keys())}")
            
            # 添加聚合表达式
            agg_exprs.append(
                agg_methods[method](pl.col(col_name)).alias(col_name)
            )
        
        if not agg_exprs:
            raise ValueError("至少需要指定一个列的聚合方式（如open='first'）")
        
        result = df.group_by(
            pl.col('asset'),
            pl.col('month')
        ).agg(agg_exprs).sort(pl.col('month'), pl.col('asset'))
        
        result = result.to_pandas()
        result['month'] = pd.PeriodIndex(result['month'], freq='M')

        return result.set_index(['month', 'asset'])
        
    def append_factor(self, data: pd.DataFrame, factor_col: str, resample_method: str) -> None:
        """将因子数据添加到回测数据(即self.data)中。

        如果因子数据日频的，
        
        Args:
            data: 因子数据，需包含'date'和'asset'列
            factor_col: 因子列名
        """
        if factor_col not in data.columns:
            raise ValueError(f"因子数据中不存在列: {factor_col}")
    
    def _get_dividend_yield(self, date: datetime.date) -> Optional[pd.Series]:
        """获取指定日期的股息率数据
        
        Args:
            date: 查询日期
            
        Returns:
            Series: 股息率数据，索引为股票代码
        """
        try:
            date_str = date.strftime('%Y%m%d')
            df = self.pro.daily_basic(trade_date=date_str)
            time.sleep(0.24)
            
            if df.empty:
                return None
                
            # 使用dv_ttm作为股息率
            dividend_data = df.set_index('ts_code')['dv_ttm'].dropna()
            dividend_data.name = 'dividend_yield'
            
            return dividend_data
            
        except Exception as e:
            print(f"获取股息率数据失败 {date}: {e}")
            return None
    
    def add_filter(self, filter_name: str, **kwargs) -> 'Moonshot':
        """添加股票筛选器
        
        Args:
            filter_name: 筛选器名称
            **kwargs: 筛选器参数
            
        Returns:
            Moonshot: 返回自身以支持链式调用
        """
        if self.data is None or self.data.empty:
            print("警告：数据为空，请先运行preprocess_data()")
            return self
            
        if filter_name == 'dividend_yield_top500':
            self._apply_dividend_yield_filter(**kwargs)
        else:
            print(f"未知的筛选器: {filter_name}")
            
        return self
    
    def _apply_dividend_yield_filter(self, top_n: int = 500) -> None:
        """应用股息率前N名筛选器
        
        Args:
            top_n: 选择前N名股票
        """
        print(f"应用股息率前{top_n}名筛选器...")
        
        if 'flag' not in self.data.columns:
            self.data['flag'] = 0
            
        for month in self.data.index.get_level_values('month').unique():
            month_data = self.data.loc[month]
            
            # 按股息率排序，选择前top_n名
            if 'dividend_yield' in month_data.columns:
                top_stocks = month_data['dividend_yield'].nlargest(top_n)
                
                # 设置flag
                for stock in top_stocks.index:
                    self.data.loc[(month, stock), 'flag'] = 1
                    
                print(f"{month.strftime('%Y-%m')}: 选中 {len(top_stocks)} 只股票")
            else:
                print(f"{month.strftime('%Y-%m')}: 无股息率数据")
    
    def calculate_returns(self) -> 'Moonshot':
        """计算策略收益率
        
        Returns:
            Moonshot: 返回自身以支持链式调用
        """
        if self.data is None or self.data.empty:
            print("警告：数据为空，无法计算收益率")
            return self
            
        print("计算策略收益率...")
        
        strategy_returns = []
        
        for month in sorted(self.data.index.get_level_values('month').unique()):
            month_data = self.data.loc[month]
            
            # 获取当月被选中的股票
            selected_stocks = month_data[month_data.get('flag', 0) == 1]
            
            if len(selected_stocks) > 0:
                # 计算月收益率 (close - open) / open
                monthly_returns = (selected_stocks['close'] - selected_stocks['open']) / selected_stocks['open']
                
                # 等权重平均
                strategy_return = monthly_returns.mean()
                strategy_returns.append((month, strategy_return))
                
                print(f"{month.strftime('%Y-%m')}: 策略收益率 {strategy_return:.4f}, 选中股票数 {len(selected_stocks)}")
            else:
                strategy_returns.append((month, 0.0))
                print(f"{month.strftime('%Y-%m')}: 无选中股票")
        
        if strategy_returns:
            self.strategy_returns = pd.Series(
                [ret for _, ret in strategy_returns],
                index=[month for month, _ in strategy_returns],
                name='strategy_returns'
            )
        
        return self
    
    def calculate_benchmark(self) -> 'Moonshot':
        """计算基准收益率（所有股票等权持仓）
        
        Returns:
            Moonshot: 返回自身以支持链式调用
        """
        if self.data is None or self.data.empty:
            print("警告：数据为空，无法计算基准收益率")
            return self
            
        print("计算基准收益率...")
        
        benchmark_returns = []
        
        for month in sorted(self.data.index.get_level_values('month').unique()):
            month_data = self.data.loc[month]
            
            # 计算所有股票的月收益率
            monthly_returns = (month_data['close'] - month_data['open']) / month_data['open']
            
            # 等权重平均
            benchmark_return = monthly_returns.mean()
            benchmark_returns.append((month, benchmark_return))
            
            print(f"{month.strftime('%Y-%m')}: 基准收益率 {benchmark_return:.4f}, 股票数 {len(month_data)}")
        
        if benchmark_returns:
            self.benchmark_returns = pd.Series(
                [ret for _, ret in benchmark_returns],
                index=[month for month, _ in benchmark_returns],
                name='benchmark_returns'
            )
        
        return self
    
    def report(self) -> Dict:
        """生成回测报告
        
        Returns:
            Dict: 包含基本指标的报告
        """
        if self.strategy_returns is None or self.benchmark_returns is None:
            print("警告：收益率数据为空，请先计算收益率")
            return {}
            
        print("\n=== 回测报告 ===")
        
        # 基本统计指标
        strategy_stats = {
            '策略年化收益率': self.strategy_returns.mean() * 12,
            '策略年化波动率': self.strategy_returns.std() * np.sqrt(12),
            '策略夏普比率': (self.strategy_returns.mean() * 12) / (self.strategy_returns.std() * np.sqrt(12)),
            '策略最大回撤': self._calculate_max_drawdown(self.strategy_returns),
        }
        
        benchmark_stats = {
            '基准年化收益率': self.benchmark_returns.mean() * 12,
            '基准年化波动率': self.benchmark_returns.std() * np.sqrt(12),
            '基准夏普比率': (self.benchmark_returns.mean() * 12) / (self.benchmark_returns.std() * np.sqrt(12)),
            '基准最大回撤': self._calculate_max_drawdown(self.benchmark_returns),
        }
        
        # 打印报告
        print("\n策略表现:")
        for key, value in strategy_stats.items():
            print(f"{key}: {value:.4f}")
            
        print("\n基准表现:")
        for key, value in benchmark_stats.items():
            print(f"{key}: {value:.4f}")
            
        # 超额收益
        excess_return = strategy_stats['策略年化收益率'] - benchmark_stats['基准年化收益率']
        print(f"\n年化超额收益率: {excess_return:.4f}")
        
        return {
            'strategy': strategy_stats,
            'benchmark': benchmark_stats,
            'excess_return': excess_return
        }
    
    def _calculate_max_drawdown(self, returns: pd.Series) -> float:
        """计算最大回撤
        
        Args:
            returns: 收益率序列
            
        Returns:
            float: 最大回撤
        """
        cumulative = (1 + returns).cumprod()
        running_max = cumulative.expanding().max()
        drawdown = (cumulative - running_max) / running_max
        return drawdown.min()


if __name__ == '__main__':
    # 示例用法
    moonshot = Moonshot('2023-01-01', '2023-03-31')
    
    # 数据预处理
    moonshot.preprocess_data()
    
    # 添加股息率筛选器
    moonshot.add_filter('dividend_yield_top500', top_n=500)
    
    # 计算收益率
    moonshot.calculate_returns()
    moonshot.calculate_benchmark()
    
    # 生成报告
    report = moonshot.report()
    report = moonshot.report()
